Stack trace:
Frame         Function      Args
0007FFFFB750  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFB750, 0007FFFFA650) msys-2.0.dll+0x1FE8E
0007FFFFB750  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA28) msys-2.0.dll+0x67F9
0007FFFFB750  000210046832 (000210286019, 0007FFFFB608, 0007FFFFB750, 000000000000) msys-2.0.dll+0x6832
0007FFFFB750  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB750  000210068E24 (0007FFFFB760, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA30  00021006A225 (0007FFFFB760, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFDCE270000 ntdll.dll
7FFDCE160000 KERNEL32.DLL
7FFDCB790000 KERNELBASE.dll
7FFDCD8C0000 USER32.dll
7FFDCB480000 win32u.dll
7FFDCE020000 GDI32.dll
7FFDCBC90000 gdi32full.dll
7FFDCB3E0000 msvcp_win.dll
7FFDCBB70000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFDCD1D0000 advapi32.dll
7FFDCCF90000 msvcrt.dll
7FFDCC3F0000 sechost.dll
7FFDCB530000 bcrypt.dll
7FFDCD090000 RPCRT4.dll
7FFDCAA60000 CRYPTBASE.DLL
7FFDCB4B0000 bcryptPrimitives.dll
7FFDCDB00000 IMM32.DLL
