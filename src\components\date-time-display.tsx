"use client";

import React, { useState, useEffect } from "react";
import { Calendar, Clock, Wifi, Battery } from "lucide-react";

export function DateTimeDisplay() {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return "Good Morning";
    if (hour < 17) return "Good Afternoon";
    return "Good Evening";
  };

  return (
    <div className="fixed top-6 left-6 z-40">
      <div className="bg-black/80 backdrop-blur-xl border border-cyan-500/30 rounded-2xl p-4 shadow-2xl">
        {/* Animated Border Glow */}
        <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/20 via-purple-500/20 to-pink-500/20 rounded-2xl blur-sm animate-pulse"></div>
        
        <div className="relative space-y-3">
          {/* Time Display */}
          <div className="flex items-center gap-3">
            <Clock className="w-5 h-5 text-cyan-400" />
            <div>
              <div className="text-2xl font-bold text-cyan-300 font-mono">
                {formatTime(currentTime)}
              </div>
              <div className="text-xs text-cyan-400/70 font-mono">
                LOCAL TIME
              </div>
            </div>
          </div>

          {/* Date Display */}
          <div className="flex items-center gap-3">
            <Calendar className="w-5 h-5 text-purple-400" />
            <div>
              <div className="text-sm text-purple-300 font-medium">
                {formatDate(currentTime)}
              </div>
              <div className="text-xs text-purple-400/70 font-mono">
                {getGreeting()}
              </div>
            </div>
          </div>

          {/* System Status */}
          <div className="flex items-center gap-4 pt-2 border-t border-cyan-500/20">
            <div className="flex items-center gap-1">
              <Wifi className="w-4 h-4 text-green-400" />
              <span className="text-xs text-green-400 font-mono">ONLINE</span>
            </div>
            <div className="flex items-center gap-1">
              <Battery className="w-4 h-4 text-blue-400" />
              <span className="text-xs text-blue-400 font-mono">SYNC</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
