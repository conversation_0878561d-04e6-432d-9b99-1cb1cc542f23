"use client";

import React, { useState } from "react";
import { Play, Pause, Music, Heart, Coffee, Zap, Moon, Sun, Headphones, Volume2 } from "lucide-react";

interface MusicCategory {
  id: string;
  title: string;
  description: string;
  videoId: string;
  duration: string;
  icon: React.ComponentType<any>;
  color: string;
  gradient: string;
  borderColor: string;
}

interface YouTubeMusicCategoriesProps {
  className?: string;
}

export function YouTubeMusicSearch({ className }: YouTubeMusicCategoriesProps) {
  const [currentPlaying, setCurrentPlaying] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);

  // Music categories with curated playlists
  const musicCategories: MusicCategory[] = [
    {
      id: "lofi",
      title: "LoFi Chill",
      description: "Relaxing beats for focus and study",
      videoId: "jfKfPfyJRdk", // LoFi Girl 24/7
      duration: "LIVE",
      icon: Coffee,
      color: "text-purple-400",
      gradient: "from-purple-500/20 to-pink-500/20",
      borderColor: "border-purple-500/30"
    },
    {
      id: "love",
      title: "Love Songs 2024",
      description: "Romantic hits and timeless classics",
      videoId: "kJQP7kiw5Fk", // Popular love song
      duration: "2:30:00",
      icon: Heart,
      color: "text-pink-400",
      gradient: "from-pink-500/20 to-red-500/20",
      borderColor: "border-pink-500/30"
    },
    {
      id: "acoustic",
      title: "Acoustic Sessions",
      description: "Unplugged and intimate performances",
      videoId: "5qap5aO4i9A", // Acoustic playlist
      duration: "1:45:00",
      icon: Music,
      color: "text-amber-400",
      gradient: "from-amber-500/20 to-orange-500/20",
      borderColor: "border-amber-500/30"
    },
    {
      id: "energetic",
      title: "Energy Boost",
      description: "High-energy tracks for motivation",
      videoId: "at-m6klQ-oA", // Energetic music
      duration: "1:20:00",
      icon: Zap,
      color: "text-yellow-400",
      gradient: "from-yellow-500/20 to-orange-500/20",
      borderColor: "border-yellow-500/30"
    },
    {
      id: "night",
      title: "Night Vibes",
      description: "Smooth tracks for evening relaxation",
      videoId: "neV3EPgvZ3g", // Night/jazz playlist
      duration: "2:00:00",
      icon: Moon,
      color: "text-blue-400",
      gradient: "from-blue-500/20 to-indigo-500/20",
      borderColor: "border-blue-500/30"
    },
    {
      id: "morning",
      title: "Morning Fresh",
      description: "Uplifting songs to start your day",
      videoId: "9bZkp7q19f0", // Upbeat morning music
      duration: "1:30:00",
      icon: Sun,
      color: "text-cyan-400",
      gradient: "from-cyan-500/20 to-blue-500/20",
      borderColor: "border-cyan-500/30"
    },
    {
      id: "focus",
      title: "Deep Focus",
      description: "Instrumental music for concentration",
      videoId: "nDq6TstdEi8", // Focus/ambient music
      duration: "3:00:00",
      icon: Headphones,
      color: "text-green-400",
      gradient: "from-green-500/20 to-emerald-500/20",
      borderColor: "border-green-500/30"
    },
    {
      id: "party",
      title: "Party Mix",
      description: "Dance hits and party anthems",
      videoId: "fJ9rUzIMcZQ", // Party music
      duration: "1:15:00",
      icon: Volume2,
      color: "text-red-400",
      gradient: "from-red-500/20 to-pink-500/20",
      borderColor: "border-red-500/30"
    }
  ];

  const playCategory = (categoryId: string) => {
    if (currentPlaying === categoryId && isPlaying) {
      // Pause current category
      setIsPlaying(false);
      setCurrentPlaying(null);
    } else {
      // Play new category
      setCurrentPlaying(categoryId);
      setIsPlaying(true);

      // In a real implementation, you'd initialize the YouTube player here
      console.log(`Playing category: ${categoryId}`);
    }
  };

  const playVideo = (videoId: string) => {
    if (currentPlaying === videoId && isPlaying) {
      // Pause current video
      setIsPlaying(false);
      setCurrentPlaying(null);
    } else {
      // Play new video
      setCurrentPlaying(videoId);
      setIsPlaying(true);
      
      // In a real implementation, you'd initialize the YouTube player here
      console.log(`Playing video: ${videoId}`);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div className={`${className}`}>

      {/* Search Interface */}
      <div className="space-y-6">
        {/* Search Bar */}
        <div className="relative">
          <div className="flex gap-3">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-purple-400 w-5 h-5" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Search for music, artists, or genres..."
                className="w-full pl-10 pr-4 py-3 bg-black/60 backdrop-blur-md border border-purple-500/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-400 transition-all duration-300"
              />
            </div>
            <button
              onClick={handleSearch}
              disabled={isSearching}
              className="px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white rounded-lg font-medium transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSearching ? "Searching..." : "Search"}
            </button>
          </div>
        </div>

        {/* Search Results */}
        {searchResults.length > 0 && (
          <div className="space-y-3">
            <h3 className="text-lg font-bold text-purple-400 font-mono">SEARCH RESULTS</h3>
            <div className="grid gap-3 max-h-96 overflow-y-auto">
              {searchResults.map((video) => (
                <div
                  key={video.id}
                  className="group relative bg-black/40 backdrop-blur-md border border-purple-500/20 rounded-lg p-4 hover:border-purple-400/50 transition-all duration-300 cursor-pointer"
                  onClick={() => playVideo(video.id)}
                >
                  <div className="flex gap-4">
                    {/* Thumbnail */}
                    <div className="relative w-24 h-16 rounded-lg overflow-hidden flex-shrink-0">
                      <Image
                        src={video.thumbnail}
                        alt={video.title}
                        width={96}
                        height={64}
                        className="w-full h-full object-cover"
                        unoptimized
                      />
                      {/* Play Overlay */}
                      <div className="absolute inset-0 bg-black/50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        {currentPlaying === video.id && isPlaying ? (
                          <Pause className="w-6 h-6 text-white" />
                        ) : (
                          <Play className="w-6 h-6 text-white" />
                        )}
                      </div>
                      {/* Duration */}
                      <div className="absolute bottom-1 right-1 bg-black/80 text-white text-xs px-1 rounded">
                        {video.duration}
                      </div>
                    </div>

                    {/* Video Info */}
                    <div className="flex-1 min-w-0">
                      <h4 className="text-white font-medium line-clamp-2 group-hover:text-purple-300 transition-colors duration-300">
                        {video.title}
                      </h4>
                      <p className="text-gray-400 text-sm mt-1">{video.channel}</p>
                      
                      {/* Playing Indicator */}
                      {currentPlaying === video.id && isPlaying && (
                        <div className="flex items-center gap-2 mt-2">
                          <div className="flex gap-1">
                            <div className="w-1 h-3 bg-purple-400 rounded-full animate-pulse"></div>
                            <div className="w-1 h-3 bg-purple-400 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                            <div className="w-1 h-3 bg-purple-400 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                          </div>
                          <span className="text-purple-400 text-sm font-medium">Now Playing</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Quick Suggestions */}
        {searchResults.length === 0 && !isSearching && (
          <div className="space-y-3">
            <h3 className="text-lg font-bold text-purple-400 font-mono">QUICK SUGGESTIONS</h3>
            <div className="grid grid-cols-3 gap-3">
              {[
                "despacito", "bohemian rhapsody", "gangnam style",
                "lofi hip hop", "jazz music", "classical music",
                "rock music", "hip hop", "electronic music",
                "adele", "queen", "nirvana",
                "study music", "rain sounds", "ambient music"
              ].map((suggestion) => (
                <button
                  key={suggestion}
                  onClick={() => {
                    setSearchQuery(suggestion);
                    setTimeout(handleSearch, 100);
                  }}
                  className="p-3 bg-black/40 backdrop-blur-md border border-purple-500/20 rounded-lg text-purple-300 hover:border-purple-400/50 hover:text-purple-200 transition-all duration-300 text-left capitalize"
                >
                  {suggestion}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
